# LogTUI 项目优化规划方案

## 🎯 项目目标重新定义

开发一个高性能、用户友好的终端日志查看工具，解决传统工具的痛点：
- 替代 `tail -f` 的单一文件限制
- 提供比 `less/vim` 更好的实时查看体验
- 支持多文件切换和关键词搜索高亮
- 保持轻量级，适合服务器环境使用

## 📦 功能规划（重新设计）

### 🚀 MVP 核心功能（第一阶段 - 2周）

| 模块 | 功能描述 | 优先级 | 技术难度 |
|------|----------|--------|----------|
| 📁 文件发现 | 自动扫描目录下的日志文件（.log, .txt, .out） | P0 | 低 |
| 📄 单文件tail | 实时监控单个文件变化，支持滚动显示 | P0 | 中 |
| 🎨 基础UI | 左侧文件列表 + 右侧内容区域 + 状态栏 | P0 | 中 |
| ⌨️ 基础交互 | 文件切换、退出、暂停滚动 | P0 | 低 |
| 🎨 日志着色 | ERROR/WARN/INFO 级别自动着色 | P1 | 低 |

### 🔍 增强功能（第二阶段 - 1.5周）

| 模块 | 功能描述 | 优先级 | 技术难度 |
|------|----------|--------|----------|
| 🔍 关键词搜索 | 实时搜索并高亮匹配内容 | P1 | 中 |
| 📊 性能优化 | Ring Buffer + 虚拟滚动 | P1 | 高 |
| ⚡ 快捷键扩展 | 搜索导航、清屏、刷新等 | P2 | 低 |
| 📈 状态信息 | 显示文件大小、行数、更新时间 | P2 | 低 |

### 🚀 高级功能（第三阶段 - 1周）

| 模块 | 功能描述 | 优先级 | 技术难度 |
|------|----------|--------|----------|
| 🔧 配置管理 | 支持配置文件自定义颜色、缓存大小等 | P2 | 中 |
| 📁 目录监控 | 自动发现新增的日志文件 | P2 | 中 |
| 🎨 主题系统 | 支持多种颜色主题 | P3 | 低 |
| 📄 历史记录 | 记住最近打开的文件和搜索关键词 | P3 | 中 |

## 🏗️ 技术架构优化

### 核心组件设计

```
logtui/
├── cmd/
│   └── main.go                 # 入口点，参数解析
├── internal/
│   ├── app/                    # 应用程序主控制器
│   │   ├── app.go             # 应用程序生命周期管理
│   │   └── config.go          # 配置管理
│   ├── ui/                     # UI组件层
│   │   ├── layout.go          # 主布局管理
│   │   ├── filelist.go        # 文件列表组件
│   │   ├── logview.go         # 日志显示组件
│   │   ├── statusbar.go       # 状态栏组件
│   │   └── search.go          # 搜索组件
│   ├── core/                   # 核心业务逻辑
│   │   ├── tailer.go          # 文件监控核心
│   │   ├── buffer.go          # Ring Buffer实现
│   │   ├── highlighter.go     # 语法高亮
│   │   └── searcher.go        # 搜索引擎
│   ├── scanner/                # 文件扫描
│   │   └── scanner.go         # 目录扫描和文件发现
│   └── utils/                  # 工具函数
│       ├── logger.go          # 日志工具
│       └── performance.go     # 性能监控
├── configs/                    # 配置文件
│   └── default.yaml
├── docs/                       # 文档
└── tests/                      # 测试文件
```

### 技术选型优化

| 组件 | 技术选择 | 理由 | 替代方案 |
|------|----------|------|----------|
| TUI框架 | tview | 成熟稳定，组件丰富 | bubbletea (更现代但学习成本高) |
| 文件监控 | hpcloud/tail | 久经考验，跨平台支持好 | fsnotify (需要自己实现tail逻辑) |
| 配置管理 | viper | 支持多种格式，功能完整 | 手动解析 (简单但功能有限) |
| 日志库 | logrus | 结构化日志，级别控制 | 标准库log (功能简单) |
| 测试框架 | testify | 断言丰富，mock支持 | 标准库testing |

## 📅 开发计划（4.5周详细规划）

### 第一周：基础架构 + 核心功能

| 天数 | 任务 | 具体内容 | 预期产出 |
|------|------|----------|----------|
| Day 1-2 | 项目架构搭建 | 目录结构、依赖管理、基础配置 | 可编译运行的框架 |
| Day 3-4 | UI布局实现 | tview布局、文件列表、日志显示区域 | 基础UI界面 |
| Day 5-7 | 文件扫描+单文件tail | 目录扫描、文件监控、实时显示 | 可监控单个文件 |

### 第二周：多文件支持 + 性能优化

| 天数 | 任务 | 具体内容 | 预期产出 |
|------|------|----------|----------|
| Day 8-9 | 多文件切换 | 文件列表交互、切换逻辑 | 支持多文件切换 |
| Day 10-11 | Ring Buffer实现 | 内存管理、滚动优化 | 大文件性能优化 |
| Day 12-14 | 日志级别着色 | 正则匹配、ANSI颜色 | 彩色日志显示 |

### 第三周：搜索功能 + 交互优化

| 天数 | 任务 | 具体内容 | 预期产出 |
|------|------|----------|----------|
| Day 15-16 | 搜索引擎 | 关键词搜索、高亮显示 | 基础搜索功能 |
| Day 17-18 | 搜索优化 | 增量搜索、性能优化 | 流畅的搜索体验 |
| Day 19-21 | 快捷键完善 | 全套快捷键、帮助系统 | 完整的交互体验 |

### 第四周：配置系统 + 高级功能

| 天数 | 任务 | 具体内容 | 预期产出 |
|------|------|----------|----------|
| Day 22-23 | 配置管理 | 配置文件、参数解析 | 可配置的应用 |
| Day 24-25 | 目录监控 | 新文件发现、动态更新 | 自动发现新日志 |
| Day 26-28 | 测试+文档 | 单元测试、集成测试、文档 | 可发布的版本 |

### 第五周（0.5周）：打包发布

| 天数 | 任务 | 具体内容 | 预期产出 |
|------|------|----------|----------|
| Day 29-31 | 发布准备 | 构建脚本、版本管理、发布包 | v1.0.0 发布版本 |

## 🚀 性能目标（实际可达成）

### 基准性能指标

| 指标 | 目标值 | 测试条件 | 优化策略 |
|------|--------|----------|----------|
| 文件切换响应 | < 200ms | 10个文件，每个100MB | 预加载 + 缓存 |
| 实时更新延迟 | < 100ms | 1000行/秒写入速度 | 批量更新 + 节流 |
| 内存占用 | < 100MB | 监控5个大文件 | Ring Buffer限制 |
| 搜索响应时间 | < 500ms | 10万行内容搜索 | 增量索引 |
| 启动时间 | < 1s | 扫描100个文件 | 异步扫描 |

### 性能优化策略

1. **内存管理**
   ```go
   const (
       MaxBufferLines = 10000    // 每个文件最多缓存1万行
       MaxFileCount   = 20       // 最多同时监控20个文件
       UpdateInterval = 50       // 50ms批量更新UI
   )
   ```

2. **虚拟滚动**
   - 只渲染可见区域的日志行
   - 使用虚拟列表减少DOM操作
   - 预加载上下文行提升滚动体验

3. **搜索优化**
   - 使用Boyer-Moore算法提升搜索速度
   - 建立简单的倒排索引
   - 异步搜索避免UI阻塞

## 🧪 测试策略

### 单元测试覆盖

- [ ] 文件扫描模块测试
- [ ] Ring Buffer功能测试  
- [ ] 搜索引擎准确性测试
- [ ] 配置管理测试

### 集成测试

- [ ] 多文件并发监控测试
- [ ] 大文件性能测试
- [ ] 内存泄漏测试
- [ ] 跨平台兼容性测试

### 压力测试

- [ ] 高频写入场景（10000行/秒）
- [ ] 大文件场景（1GB+文件）
- [ ] 长时间运行稳定性（24小时+）

## 📋 里程碑检查点

### Milestone 1: MVP可用版本 (2周后)
- ✅ 基础UI完成
- ✅ 单文件实时监控
- ✅ 多文件切换
- ✅ 基础快捷键

### Milestone 2: 功能完整版本 (3.5周后)  
- ✅ 搜索高亮功能
- ✅ 性能优化完成
- ✅ 配置系统
- ✅ 完整文档

### Milestone 3: 发布版本 (4.5周后)
- ✅ 全面测试通过
- ✅ 跨平台构建
- ✅ 用户手册
- ✅ 发布包准备

## 🎯 成功标准

1. **功能完整性**: 所有P0和P1功能正常工作
2. **性能达标**: 满足上述性能基准
3. **用户体验**: 操作流畅，响应及时
4. **稳定性**: 长时间运行无崩溃
5. **可维护性**: 代码结构清晰，测试覆盖充分

这个优化后的规划更加务实和可执行，时间安排更合理，技术风险更可控。
