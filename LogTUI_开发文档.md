# LogTUI - 简单高效的日志查看工具

## 🎯 项目目标

开发一个**单文件日志查看工具**，解决vim/tail查看日志的痛点：
- ❌ vim看日志：无实时更新，搜索不方便，无高亮
- ❌ tail -f：无历史浏览，无搜索，无高亮，操作单一
- ✅ LogTUI：实时更新 + 历史浏览 + 快速搜索 + 日志高亮

## 📦 核心功能

### 🚀 必需功能 (MVP)
| 功能 | 描述 | 优先级 |
|------|------|--------|
| 📄 文件打开 | 支持指定文件路径打开日志文件 | P0 |
| 👁️ 实时监控 | 自动tail文件变化，实时显示新内容 | P0 |
| 📜 历史浏览 | 上下滚动查看历史日志内容 | P0 |
| 🔍 快速搜索 | 关键词搜索并高亮显示 | P0 |
| 🎨 日志高亮 | ERROR/WARN/INFO等级别自动着色 | P0 |
| ⌨️ 基础操作 | 退出、暂停滚动、清屏等 | P0 |

### 🔧 增强功能
| 功能 | 描述 | 优先级 |
|------|------|--------|
| 📊 状态显示 | 文件大小、行数、搜索结果数量 | P1 |
| 🎯 搜索导航 | 上一个/下一个搜索结果跳转 | P1 |
| 📋 内容复制 | 选中文本复制到剪贴板 | P2 |
| 🎨 自定义配置 | 颜色主题、快捷键配置 | P2 |

## 🏗️ 技术架构

### 技术选型
- **语言**: Go (性能好，部署简单)
- **TUI框架**: tview (成熟稳定)
- **文件监控**: hpcloud/tail (专业tail库)
- **配置管理**: 简单的YAML配置

### 项目结构
```
logtui/
├── cmd/
│   └── main.go              # 程序入口
├── internal/
│   ├── app/
│   │   └── app.go          # 主应用逻辑
│   ├── ui/
│   │   ├── viewer.go       # 日志显示组件
│   │   ├── search.go       # 搜索组件
│   │   └── status.go       # 状态栏组件
│   ├── core/
│   │   ├── tailer.go       # 文件监控
│   │   ├── highlighter.go  # 语法高亮
│   │   └── searcher.go     # 搜索引擎
│   └── config/
│       └── config.go       # 配置管理
├── configs/
│   └── default.yaml        # 默认配置
└── README.md
```

## 📅 开发计划 (2周)

### 第1周：核心功能
| 天数 | 任务 | 产出 |
|------|------|------|
| Day 1-2 | 项目搭建 + 基础UI | 可运行的框架 |
| Day 3-4 | 文件打开 + tail监控 | 实时显示日志 |
| Day 5-7 | 日志高亮 + 历史浏览 | 基本可用版本 |

### 第2周：搜索与优化
| 天数 | 任务 | 产出 |
|------|------|------|
| Day 8-9 | 搜索功能 + 高亮 | 搜索可用 |
| Day 10-11 | 快捷键 + 状态栏 | 完整交互 |
| Day 12-14 | 性能优化 + 测试 | 发布版本 |

## 🎨 界面设计

```
┌─────────────────────────────────────────────────────────┐
│                    LogTUI v1.0.0                       │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  2024-08-03 10:30:15 [INFO] Application started        │
│  2024-08-03 10:30:16 [WARN] High memory usage          │
│  2024-08-03 10:30:17 [ERROR] Database connection failed│
│  2024-08-03 10:30:18 [INFO] Retrying connection...     │
│  ▶ 2024-08-03 10:30:19 [INFO] New log entry            │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ 📄 app.log | 📏 1.2MB | 📊 1234行 | 🔍 error: 3个结果    │
├─────────────────────────────────────────────────────────┤
│ 💡 /搜索 ↑↓导航 Space暂停 q退出 ?帮助                    │
└─────────────────────────────────────────────────────────┘
```

## ⌨️ 快捷键设计

| 快捷键 | 功能 |
|--------|------|
| `q` | 退出程序 |
| `/` | 进入搜索模式 |
| `n` | 下一个搜索结果 |
| `N` | 上一个搜索结果 |
| `Space` | 暂停/恢复实时滚动 |
| `g` | 跳转到文件开头 |
| `G` | 跳转到文件末尾 |
| `c` | 清空屏幕 |
| `r` | 刷新文件 |
| `?` | 显示帮助 |

## 🎨 日志高亮规则

```go
var LogLevelColors = map[string]tcell.Color{
    "ERROR":   tcell.ColorRed,
    "FATAL":   tcell.ColorDarkRed,
    "WARN":    tcell.ColorYellow,
    "WARNING": tcell.ColorYellow,
    "INFO":    tcell.ColorGreen,
    "DEBUG":   tcell.ColorBlue,
    "TRACE":   tcell.ColorGray,
}
```

## 🚀 性能目标

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 启动时间 | < 500ms | 打开文件到显示内容 |
| 搜索响应 | < 200ms | 10万行内容搜索 |
| 内存占用 | < 50MB | 单文件监控 |
| 大文件支持 | 500MB+ | 使用Ring Buffer |

## 🧪 测试计划

### 功能测试
- [ ] 文件打开和监控
- [ ] 实时更新显示
- [ ] 搜索和高亮
- [ ] 快捷键操作
- [ ] 日志级别着色

### 性能测试
- [ ] 大文件加载测试 (100MB+)
- [ ] 高频写入测试 (1000行/秒)
- [ ] 搜索性能测试
- [ ] 内存使用测试

## 📋 验收标准

1. **功能完整**: 所有P0功能正常工作
2. **操作简单**: 比vim/tail更容易使用
3. **性能良好**: 满足性能目标
4. **稳定可靠**: 正常使用无崩溃

## 🎯 成功标准

**解决vim/tail的痛点**:
- ✅ 实时更新 (vs vim需要手动刷新)
- ✅ 快速搜索 (vs vim搜索复杂)
- ✅ 日志高亮 (vs tail无颜色)
- ✅ 历史浏览 (vs tail只能看新内容)
- ✅ 操作简单 (vs vim学习成本高)

这就是一个**专注、简单、实用**的日志查看工具！
