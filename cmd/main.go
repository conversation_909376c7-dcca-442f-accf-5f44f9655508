package main

import (
	"flag"
	"fmt"
	"os"

	"logtui/internal/app"
)

const (
	Version = "1.0.0"
)

func main() {
	var (
		filePath    = flag.String("file", "", "日志文件路径 (必需)")
		showVersion = flag.Bool("version", false, "显示版本信息")
		showHelp    = flag.Bool("help", false, "显示帮助信息")
	)
	
	flag.Parse()

	// 显示版本信息
	if *showVersion {
		fmt.Printf("LogTUI v%s\n", Version)
		fmt.Println("简单高效的日志查看工具")
		os.Exit(0)
	}

	// 显示帮助信息
	if *showHelp {
		printHelp()
		os.Exit(0)
	}

	// 检查文件路径参数
	if *filePath == "" {
		fmt.Println("错误: 请指定日志文件路径")
		fmt.Println("使用 -help 查看帮助信息")
		os.Exit(1)
	}

	// 检查文件是否存在
	if _, err := os.Stat(*filePath); os.IsNotExist(err) {
		fmt.Printf("错误: 文件不存在: %s\n", *filePath)
		os.Exit(1)
	}

	// 启动应用
	application := app.New(*filePath)
	if err := application.Run(); err != nil {
		fmt.Printf("错误: %v\n", err)
		os.Exit(1)
	}
}

func printHelp() {
	fmt.Printf("LogTUI v%s - 简单高效的日志查看工具\n\n", Version)
	fmt.Println("用法:")
	fmt.Println("  logtui -file <日志文件路径>")
	fmt.Println("")
	fmt.Println("选项:")
	fmt.Println("  -file string    指定要查看的日志文件路径 (必需)")
	fmt.Println("  -version        显示版本信息")
	fmt.Println("  -help           显示此帮助信息")
	fmt.Println("")
	fmt.Println("快捷键:")
	fmt.Println("  q               退出程序")
	fmt.Println("  /               进入搜索模式")
	fmt.Println("  n               下一个搜索结果")
	fmt.Println("  N               上一个搜索结果")
	fmt.Println("  Space           暂停/恢复实时滚动")
	fmt.Println("  g               跳转到文件开头")
	fmt.Println("  G               跳转到文件末尾")
	fmt.Println("  c               清空屏幕")
	fmt.Println("  r               刷新文件")
	fmt.Println("  ?               显示帮助")
	fmt.Println("")
	fmt.Println("示例:")
	fmt.Println("  logtui -file /var/log/app.log")
	fmt.Println("  logtui -file ./error.log")
}
