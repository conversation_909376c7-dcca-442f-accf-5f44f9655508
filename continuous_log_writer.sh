#!/bin/bash

# 持续写入日志的脚本
LOG_FILE="realtime_test.log"
COUNTER=1

echo "=== 开始持续写入日志到 $LOG_FILE ==="
echo "按 Ctrl+C 停止"
echo

# 获取当前时间戳函数
get_timestamp() {
    date '+%Y-%m-%d %H:%M:%S'
}

# 随机选择日志级别
get_random_level() {
    levels=("INFO" "WARN" "ERROR" "DEBUG")
    echo ${levels[$((RANDOM % 4))]}
}

# 随机选择日志消息
get_random_message() {
    messages=(
        "Processing user request"
        "Database query executed"
        "Cache miss detected"
        "Memory usage: 75%"
        "Connection established"
        "Task completed successfully"
        "Validation failed"
        "Backup process started"
        "Configuration updated"
        "Service health check"
    )
    echo ${messages[$((RANDOM % 10))]}
}

# 持续写入循环
while true; do
    timestamp=$(get_timestamp)
    level=$(get_random_level)
    message=$(get_random_message)
    
    echo "$timestamp [$level] $message - Entry #$COUNTER" >> $LOG_FILE
    echo "写入: $timestamp [$level] $message - Entry #$COUNTER"
    
    COUNTER=$((COUNTER + 1))
    
    # 每秒写入一条
    sleep 1
done
