package app

import (
	"fmt"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"

	"logtui/internal/ui"
)

// App 主应用程序结构
type App struct {
	filePath string
	tviewApp *tview.Application
	ui       *ui.UI
}

// New 创建新的应用实例
func New(filePath string) *App {
	return &App{
		filePath: filePath,
		tviewApp: tview.NewApplication(),
	}
}

// Run 启动应用程序
func (a *App) Run() error {
	// 创建UI
	var err error
	a.ui, err = ui.New(a.filePath)
	if err != nil {
		return fmt.Errorf("创建UI失败: %w", err)
	}

	// 设置全局快捷键
	a.setupGlobalKeys()

	// 设置根组件并运行
	a.tviewApp.SetRoot(a.ui.GetRoot(), true)
	
	// 启动UI组件
	if err := a.ui.Start(); err != nil {
		return fmt.Errorf("启动UI失败: %w", err)
	}

	// 运行应用
	if err := a.tviewApp.Run(); err != nil {
		return fmt.Errorf("运行应用失败: %w", err)
	}

	return nil
}

// setupGlobalKeys 设置全局快捷键
func (a *App) setupGlobalKeys() {
	a.tviewApp.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		switch event.Rune() {
		case 'q', 'Q':
			// 退出程序
			a.tviewApp.Stop()
			return nil
		case '?':
			// 显示帮助
			a.showHelp()
			return nil
		}

		switch event.Key() {
		case tcell.KeyEscape:
			// ESC键处理
			if a.ui != nil {
				a.ui.HandleEscape()
			}
			return nil
		}

		return event
	})
}

// showHelp 显示帮助对话框
func (a *App) showHelp() {
	helpText := `LogTUI v1.0.0 - 快捷键帮助

基本操作:
  q, Q          退出程序
  ?             显示此帮助

导航操作:
  ↑↓            上下滚动
  Page Up/Down  翻页
  g             跳转到开头
  G             跳转到末尾

搜索操作:
  /             进入搜索模式
  n             下一个搜索结果
  N             上一个搜索结果
  ESC           退出搜索模式

其他操作:
  Space         暂停/恢复实时滚动
  c             清空屏幕
  r             刷新文件

按任意键关闭帮助...`

	modal := tview.NewModal().
		SetText(helpText).
		AddButtons([]string{"关闭"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.tviewApp.SetRoot(a.ui.GetRoot(), true)
		})

	a.tviewApp.SetRoot(modal, true)
}
