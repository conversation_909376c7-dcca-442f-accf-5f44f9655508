package core

import (
	"bufio"
	"fmt"
	"os"
	"sync"

	"github.com/hpcloud/tail"
)

// LogLine 表示一行日志
type LogLine struct {
	Text string
	Raw  string
}

// Tailer 文件监控器
type Tailer struct {
	filePath string
	tail     *tail.Tail
	
	// 通道
	logChan    chan LogLine
	stopChan   chan struct{}
	
	// 状态
	isRunning  bool
	mutex      sync.RWMutex
	
	// 回调函数
	onNewLine  func(LogLine)
	onError    func(error)
}

// NewTailer 创建新的文件监控器
func NewTailer(filePath string) *Tailer {
	return &Tailer{
		filePath: filePath,
		logChan:  make(chan LogLine, 100),
		stopChan: make(chan struct{}),
	}
}

// SetOnNewLine 设置新行回调
func (t *Tailer) SetOnNewLine(callback func(LogLine)) {
	t.onNewLine = callback
}

// SetOnError 设置错误回调
func (t *Tailer) SetOnError(callback func(error)) {
	t.onError = callback
}

// LoadExistingContent 加载现有文件内容
func (t *Tailer) LoadExistingContent() ([]LogLine, error) {
	file, err := os.Open(t.filePath)
	if err != nil {
		return nil, fmt.Errorf("打开文件失败: %w", err)
	}
	defer file.Close()

	var lines []LogLine
	scanner := bufio.NewScanner(file)
	
	for scanner.Scan() {
		line := scanner.Text()
		logLine := LogLine{
			Text: line,
			Raw:  line,
		}
		lines = append(lines, logLine)
	}

	if err := scanner.Err(); err != nil {
		return nil, fmt.Errorf("读取文件失败: %w", err)
	}

	return lines, nil
}

// Start 开始监控文件
func (t *Tailer) Start() error {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	if t.isRunning {
		return fmt.Errorf("tailer已经在运行")
	}

	// 配置tail选项
	config := tail.Config{
		Follow:    true,  // 跟随文件
		ReOpen:    true,  // 文件被重新创建时重新打开
		MustExist: true,  // 文件必须存在
		Poll:      false, // 使用inotify而不是轮询
		Location:  &tail.SeekInfo{Offset: 0, Whence: 2}, // 从文件末尾开始
	}

	var err error
	t.tail, err = tail.TailFile(t.filePath, config)
	if err != nil {
		return fmt.Errorf("启动tail失败: %w", err)
	}

	t.isRunning = true

	// 启动监控goroutine
	go t.monitor()

	return nil
}

// Stop 停止监控
func (t *Tailer) Stop() error {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	if !t.isRunning {
		return nil
	}

	// 发送停止信号
	close(t.stopChan)

	// 停止tail
	if t.tail != nil {
		err := t.tail.Stop()
		if err != nil {
			return fmt.Errorf("停止tail失败: %w", err)
		}
	}

	t.isRunning = false
	return nil
}

// IsRunning 检查是否正在运行
func (t *Tailer) IsRunning() bool {
	t.mutex.RLock()
	defer t.mutex.RUnlock()
	return t.isRunning
}

// monitor 监控文件变化
func (t *Tailer) monitor() {
	defer func() {
		t.mutex.Lock()
		t.isRunning = false
		t.mutex.Unlock()
	}()

	for {
		select {
		case <-t.stopChan:
			return
		case line, ok := <-t.tail.Lines:
			if !ok {
				// 通道关闭
				return
			}
			
			if line.Err != nil {
				if t.onError != nil {
					t.onError(line.Err)
				}
				continue
			}

			logLine := LogLine{
				Text: line.Text,
				Raw:  line.Text,
			}

			// 发送到通道
			select {
			case t.logChan <- logLine:
			default:
				// 通道满了，丢弃旧的日志
			}

			// 调用回调函数
			if t.onNewLine != nil {
				t.onNewLine(logLine)
			}
		}
	}
}

// GetLogChan 获取日志通道
func (t *Tailer) GetLogChan() <-chan LogLine {
	return t.logChan
}
