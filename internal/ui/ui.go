package ui

import (
	"fmt"
	"logtui/internal/core"
	"path/filepath"
	"strings"

	"github.com/rivo/tview"
)

// UI 主界面结构
type UI struct {
	filePath string

	// tview组件
	app       *tview.Application
	flex      *tview.Flex
	logView   *tview.TextView
	statusBar *tview.TextView

	// 核心组件
	tailer *core.Tailer

	// 状态
	isPaused        bool
	searchMode      bool
	searchTerm      string
	lineCount       int
	showLineNumbers bool
}

// New 创建新的UI实例
func New(filePath string) (*UI, error) {
	ui := &UI{
		filePath:        filePath,
		showLineNumbers: true, // 默认显示行号
	}

	// 创建tailer
	ui.tailer = core.NewTailer(filePath)

	ui.setupComponents()
	ui.setupLayout()
	ui.setupTailer()

	return ui, nil
}

// setupComponents 初始化UI组件
func (ui *UI) setupComponents() {
	// 创建日志显示区域
	ui.logView = tview.NewTextView().
		SetDynamicColors(true).
		SetRegions(true).
		SetWordWrap(true).
		SetScrollable(true)

	ui.logView.
		SetBorder(true).
		SetTitle(" 日志内容 ").
		SetTitleAlign(tview.AlignLeft)

	// 创建状态栏
	ui.statusBar = tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignLeft)

	ui.statusBar.
		SetBorder(true).
		SetTitle(" 状态 ")

	// 设置初始状态
	ui.updateStatusBar()
}

// setupLayout 设置布局
func (ui *UI) setupLayout() {
	// 创建主布局
	ui.flex = tview.NewFlex().
		SetDirection(tview.FlexRow).
		AddItem(ui.logView, 0, 1, true).
		AddItem(ui.statusBar, 3, 0, false)
}

// GetRoot 获取根组件
func (ui *UI) GetRoot() tview.Primitive {
	return ui.flex
}

// setupTailer 设置tailer回调
func (ui *UI) setupTailer() {
	// 设置新行回调
	ui.tailer.SetOnNewLine(func(line core.LogLine) {
		if !ui.isPaused {
			ui.appendLogLine(line.Text)
		}
	})

	// 设置错误回调
	ui.tailer.SetOnError(func(err error) {
		ui.showError(fmt.Sprintf("文件监控错误: %v", err))
	})
}

// Start 启动UI组件
func (ui *UI) Start() error {
	// 显示初始消息
	ui.logView.SetText("正在加载日志文件...\n")
	ui.updateStatusBar()

	// 加载现有文件内容
	if err := ui.loadExistingContent(); err != nil {
		return fmt.Errorf("加载文件内容失败: %w", err)
	}

	// 启动tail监控
	if err := ui.tailer.Start(); err != nil {
		return fmt.Errorf("启动文件监控失败: %w", err)
	}

	return nil
}

// HandleEscape 处理ESC键
func (ui *UI) HandleEscape() {
	if ui.searchMode {
		ui.exitSearchMode()
	}
}

// updateStatusBar 更新状态栏
func (ui *UI) updateStatusBar() {
	fileName := filepath.Base(ui.filePath)

	status := fmt.Sprintf("📄 %s | 📊 %d行", fileName, ui.lineCount)

	// 显示行号状态
	if ui.showLineNumbers {
		status += " | 🔢 行号"
	} else {
		status += " | 🔢 无行号"
	}

	if ui.isPaused {
		status += " | ⏸️ 已暂停"
	} else {
		status += " | ▶️ 实时"
	}

	if ui.searchMode {
		status += fmt.Sprintf(" | 🔍 搜索: %s", ui.searchTerm)
	}

	status += " | 💡 按 ? 查看帮助"

	ui.statusBar.SetText(status)
}

// exitSearchMode 退出搜索模式
func (ui *UI) exitSearchMode() {
	ui.searchMode = false
	ui.searchTerm = ""
	ui.updateStatusBar()
}

// TogglePause 切换暂停状态
func (ui *UI) TogglePause() {
	ui.isPaused = !ui.isPaused
	ui.updateStatusBar()
}

// SetSearchMode 设置搜索模式
func (ui *UI) SetSearchMode(term string) {
	ui.searchMode = true
	ui.searchTerm = term
	ui.updateStatusBar()
}

// AppendLog 添加日志内容
func (ui *UI) AppendLog(text string) {
	if !ui.isPaused {
		fmt.Fprint(ui.logView, text)
	}
}

// ClearLog 清空日志显示
func (ui *UI) ClearLog() {
	ui.logView.Clear()
	ui.updateStatusBar()
}

// GetLogView 获取日志视图组件
func (ui *UI) GetLogView() *tview.TextView {
	return ui.logView
}

// loadExistingContent 加载现有文件内容
func (ui *UI) loadExistingContent() error {
	lines, err := ui.tailer.LoadExistingContent()
	if err != nil {
		return err
	}

	ui.logView.Clear()
	ui.lineCount = 0 // 重置行号计数

	for _, line := range lines {
		ui.appendLogLine(line.Text)
	}

	return nil
}

// appendLogLine 添加一行日志
func (ui *UI) appendLogLine(text string) {
	ui.lineCount++

	// 添加颜色高亮
	coloredText := ui.colorizeLogLine(text)

	// 格式化输出（包含行号）
	formattedLine := ui.formatLogLine(ui.lineCount, coloredText)

	fmt.Fprintf(ui.logView, "%s\n", formattedLine)
	ui.updateStatusBar()
}

// formatLogLine 格式化日志行（添加行号）
func (ui *UI) formatLogLine(lineNum int, text string) string {
	if ui.showLineNumbers {
		// 计算行号宽度（至少4位，最多6位）
		lineNumWidth := 4
		if lineNum >= 10000 {
			lineNumWidth = 5
		}
		if lineNum >= 100000 {
			lineNumWidth = 6
		}

		// 格式化行号，右对齐
		lineNumStr := fmt.Sprintf("[gray]%*d[white] ", lineNumWidth, lineNum)
		return lineNumStr + text
	}
	return text
}

// colorizeLogLine 为日志行添加颜色
func (ui *UI) colorizeLogLine(text string) string {
	// 简单的日志级别着色
	lowerText := strings.ToLower(text)

	if strings.Contains(lowerText, "error") || strings.Contains(lowerText, "fatal") {
		return fmt.Sprintf("[red]%s[white]", text)
	} else if strings.Contains(lowerText, "warn") {
		return fmt.Sprintf("[yellow]%s[white]", text)
	} else if strings.Contains(lowerText, "info") {
		return fmt.Sprintf("[green]%s[white]", text)
	} else if strings.Contains(lowerText, "debug") {
		return fmt.Sprintf("[blue]%s[white]", text)
	}

	return text
}

// showError 显示错误信息
func (ui *UI) showError(message string) {
	fmt.Fprintf(ui.logView, "[red]%s[white]\n", message)
}

// ToggleLineNumbers 切换行号显示
func (ui *UI) ToggleLineNumbers() {
	ui.showLineNumbers = !ui.showLineNumbers
	ui.updateStatusBar()

	// 重新加载内容以应用行号变化
	if err := ui.loadExistingContent(); err != nil {
		ui.showError(fmt.Sprintf("重新加载失败: %v", err))
	}
}

// Refresh 刷新文件内容
func (ui *UI) Refresh() {
	if err := ui.loadExistingContent(); err != nil {
		ui.showError(fmt.Sprintf("刷新失败: %v", err))
	}
}

// Stop 停止UI组件
func (ui *UI) Stop() error {
	if ui.tailer != nil {
		return ui.tailer.Stop()
	}
	return nil
}
