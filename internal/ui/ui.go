package ui

import (
	"fmt"
	"path/filepath"

	"github.com/rivo/tview"
)

// UI 主界面结构
type UI struct {
	filePath string

	// tview组件
	app       *tview.Application
	flex      *tview.Flex
	logView   *tview.TextView
	statusBar *tview.TextView

	// 状态
	isPaused   bool
	searchMode bool
	searchTerm string
}

// New 创建新的UI实例
func New(filePath string) (*UI, error) {
	ui := &UI{
		filePath: filePath,
	}

	ui.setupComponents()
	ui.setupLayout()

	return ui, nil
}

// setupComponents 初始化UI组件
func (ui *UI) setupComponents() {
	// 创建日志显示区域
	ui.logView = tview.NewTextView().
		SetDynamicColors(true).
		SetRegions(true).
		SetWordWrap(true).
		SetScrollable(true)

	ui.logView.
		SetBorder(true).
		SetTitle(" 日志内容 ").
		SetTitleAlign(tview.AlignLeft)

	// 创建状态栏
	ui.statusBar = tview.NewTextView().
		SetDynamicColors(true).
		SetTextAlign(tview.AlignLeft)

	ui.statusBar.
		SetBorder(true).
		SetTitle(" 状态 ")

	// 设置初始状态
	ui.updateStatusBar()
}

// setupLayout 设置布局
func (ui *UI) setupLayout() {
	// 创建主布局
	ui.flex = tview.NewFlex().
		SetDirection(tview.FlexRow).
		AddItem(ui.logView, 0, 1, true).
		AddItem(ui.statusBar, 3, 0, false)
}

// GetRoot 获取根组件
func (ui *UI) GetRoot() tview.Primitive {
	return ui.flex
}

// Start 启动UI组件
func (ui *UI) Start() error {
	// 显示初始消息
	ui.logView.SetText("正在加载日志文件...\n")
	ui.updateStatusBar()

	// TODO: 这里后续会添加文件监控逻辑
	ui.logView.SetText(fmt.Sprintf("LogTUI v1.0.0\n\n文件: %s\n\n等待日志内容...\n\n按 ? 查看帮助", ui.filePath))

	return nil
}

// HandleEscape 处理ESC键
func (ui *UI) HandleEscape() {
	if ui.searchMode {
		ui.exitSearchMode()
	}
}

// updateStatusBar 更新状态栏
func (ui *UI) updateStatusBar() {
	fileName := filepath.Base(ui.filePath)

	status := fmt.Sprintf("📄 %s", fileName)

	if ui.isPaused {
		status += " | ⏸️ 已暂停"
	} else {
		status += " | ▶️ 实时"
	}

	if ui.searchMode {
		status += fmt.Sprintf(" | 🔍 搜索: %s", ui.searchTerm)
	}

	status += " | 💡 按 ? 查看帮助"

	ui.statusBar.SetText(status)
}

// exitSearchMode 退出搜索模式
func (ui *UI) exitSearchMode() {
	ui.searchMode = false
	ui.searchTerm = ""
	ui.updateStatusBar()
}

// TogglePause 切换暂停状态
func (ui *UI) TogglePause() {
	ui.isPaused = !ui.isPaused
	ui.updateStatusBar()
}

// SetSearchMode 设置搜索模式
func (ui *UI) SetSearchMode(term string) {
	ui.searchMode = true
	ui.searchTerm = term
	ui.updateStatusBar()
}

// AppendLog 添加日志内容
func (ui *UI) AppendLog(text string) {
	if !ui.isPaused {
		fmt.Fprint(ui.logView, text)
	}
}

// ClearLog 清空日志显示
func (ui *UI) ClearLog() {
	ui.logView.Clear()
	ui.updateStatusBar()
}

// GetLogView 获取日志视图组件
func (ui *UI) GetLogView() *tview.TextView {
	return ui.logView
}
