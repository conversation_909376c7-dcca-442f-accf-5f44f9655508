2024-08-03 10:30:15 [INFO] Application started successfully
2024-08-03 10:30:16 [INFO] Loading configuration from config.yaml
2024-08-03 10:30:17 [WARN] Configuration file not found, using defaults
2024-08-03 10:30:18 [INFO] Database connection established
2024-08-03 10:30:19 [INFO] Starting HTTP server on port 8080
2024-08-03 10:30:20 [INFO] Server is ready to accept connections
2024-08-03 10:31:15 [INFO] Received request: GET /api/users
2024-08-03 10:31:16 [INFO] Processing user list request
2024-08-03 10:31:17 [WARN] High memory usage detected: 85%
2024-08-03 10:31:18 [INFO] Request completed successfully
2024-08-03 10:32:20 [ERROR] Database connection lost
2024-08-03 10:32:21 [INFO] Attempting to reconnect to database
2024-08-03 10:32:22 [INFO] Database connection restored
2024-08-03 10:32:23 [INFO] Received request: POST /api/login
2024-08-03 10:32:24 [WARN] Invalid login attempt from IP: *************
2024-08-03 10:32:25 [ERROR] Authentication failed for user: admin
2024-08-03 10:33:30 [INFO] Received request: GET /api/status
2024-08-03 10:33:31 [INFO] System status: OK
2024-08-03 10:33:32 [DEBUG] Memory usage: 45%, CPU usage: 12%
2024-08-03 10:33:33 [INFO] Request completed successfully
