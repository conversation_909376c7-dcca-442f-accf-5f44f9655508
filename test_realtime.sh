#!/bin/bash

# 实时性测试脚本
LOG_FILE="realtime_test.log"

echo "=== LogTUI 实时性测试 ==="
echo "测试文件: $LOG_FILE"
echo

# 清空测试文件
echo "2024-08-03 15:40:00 [INFO] Starting realtime test" > $LOG_FILE
echo "2024-08-03 15:40:01 [INFO] Initial log entries" >> $LOG_FILE
echo "2024-08-03 15:40:02 [INFO] Ready for real-time testing" >> $LOG_FILE

echo "初始文件内容:"
cat $LOG_FILE
echo
echo "文件行数: $(wc -l < $LOG_FILE)"
echo

# 等待一下，然后添加新内容
echo "等待2秒后开始添加新日志..."
sleep 2

echo "添加第1条实时日志..."
echo "2024-08-03 15:40:05 [INFO] First real-time entry" >> $LOG_FILE

sleep 1
echo "添加第2条实时日志..."
echo "2024-08-03 15:40:06 [WARN] Warning message for testing" >> $LOG_FILE

sleep 1
echo "添加第3条实时日志..."
echo "2024-08-03 15:40:07 [ERROR] Error message for testing" >> $LOG_FILE

sleep 1
echo "添加第4条实时日志..."
echo "2024-08-03 15:40:08 [DEBUG] Debug message for testing" >> $LOG_FILE

sleep 1
echo "添加第5条实时日志..."
echo "2024-08-03 15:40:09 [INFO] Final test entry" >> $LOG_FILE

echo
echo "最终文件内容:"
cat $LOG_FILE
echo
echo "最终文件行数: $(wc -l < $LOG_FILE)"
echo
echo "=== 测试完成 ==="
